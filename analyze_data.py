#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
E题数据分析脚本
用于分析交通流量管控相关数据并生成论文内容
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_analyze_data():
    """加载并分析数据文件"""
    
    # 数据文件路径
    data_dir = Path("E题内容/E题")
    csv_file = data_dir / "附件2.csv"
    excel_file = data_dir / "附件1.xlsx"
    
    print("=== E题：交通流量管控数据分析 ===\n")
    
    # 检查文件是否存在
    if csv_file.exists():
        print(f"发现CSV文件: {csv_file}")
        try:
            # 尝试不同编码读取CSV文件
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            df_csv = None
            
            for encoding in encodings:
                try:
                    df_csv = pd.read_csv(csv_file, encoding=encoding)
                    print(f"成功使用 {encoding} 编码读取CSV文件")
                    break
                except UnicodeDecodeError:
                    continue
            
            if df_csv is not None:
                print(f"CSV文件形状: {df_csv.shape}")
                print(f"CSV文件列名: {list(df_csv.columns)}")
                print("\nCSV文件前5行:")
                print(df_csv.head())
                print("\nCSV文件基本统计信息:")
                print(df_csv.describe())
                
                # 保存CSV分析结果
                with open("csv_analysis.txt", "w", encoding="utf-8") as f:
                    f.write("=== CSV文件分析结果 ===\n\n")
                    f.write(f"文件形状: {df_csv.shape}\n")
                    f.write(f"列名: {list(df_csv.columns)}\n\n")
                    f.write("前10行数据:\n")
                    f.write(df_csv.head(10).to_string())
                    f.write("\n\n基本统计信息:\n")
                    f.write(df_csv.describe().to_string())
                    
                    # 检查数据类型
                    f.write("\n\n数据类型:\n")
                    f.write(df_csv.dtypes.to_string())
                    
                    # 检查缺失值
                    f.write("\n\n缺失值统计:\n")
                    f.write(df_csv.isnull().sum().to_string())
                
            else:
                print("无法读取CSV文件，尝试所有编码都失败")
                
        except Exception as e:
            print(f"读取CSV文件时出错: {e}")
    else:
        print(f"CSV文件不存在: {csv_file}")
    
    # 读取Excel文件
    if excel_file.exists():
        print(f"\n发现Excel文件: {excel_file}")
        try:
            # 读取Excel文件的所有工作表
            excel_data = pd.ExcelFile(excel_file)
            print(f"Excel工作表: {excel_data.sheet_names}")
            
            # 读取每个工作表
            for sheet_name in excel_data.sheet_names:
                print(f"\n--- 工作表: {sheet_name} ---")
                df_excel = pd.read_excel(excel_file, sheet_name=sheet_name)
                print(f"形状: {df_excel.shape}")
                print(f"列名: {list(df_excel.columns)}")
                print("前5行:")
                print(df_excel.head())
                
                # 保存Excel分析结果
                with open(f"excel_analysis_{sheet_name}.txt", "w", encoding="utf-8") as f:
                    f.write(f"=== Excel工作表 {sheet_name} 分析结果 ===\n\n")
                    f.write(f"文件形状: {df_excel.shape}\n")
                    f.write(f"列名: {list(df_excel.columns)}\n\n")
                    f.write("前10行数据:\n")
                    f.write(df_excel.head(10).to_string())
                    f.write("\n\n基本统计信息:\n")
                    if df_excel.select_dtypes(include=[np.number]).shape[1] > 0:
                        f.write(df_excel.describe().to_string())
                    else:
                        f.write("无数值型数据")
                    
                    # 检查数据类型
                    f.write("\n\n数据类型:\n")
                    f.write(df_excel.dtypes.to_string())
                    
                    # 检查缺失值
                    f.write("\n\n缺失值统计:\n")
                    f.write(df_excel.isnull().sum().to_string())
                
        except Exception as e:
            print(f"读取Excel文件时出错: {e}")
    else:
        print(f"Excel文件不存在: {excel_file}")

def generate_paper_outline():
    """生成论文大纲"""
    
    outline = """
# 交通流量管控问题研究论文大纲

## 1. 摘要
- 研究背景：小镇景区交通流量管控问题
- 研究方法：数据分析、数学建模
- 主要结论：优化管控策略建议
- 关键词：交通流量、管控策略、数据分析、优化模型

## 2. 问题重述
- 小镇景区交通拥堵问题
- 现有管控措施的局限性
- 需要解决的核心问题

## 3. 问题分析
### 3.1 交通流量特征分析
- 时间分布特征
- 空间分布特征
- 影响因素分析

### 3.2 现有管控措施评估
- 管控效果分析
- 存在问题识别

## 4. 模型建立与求解
### 4.1 交通流量预测模型
- 基于历史数据的时间序列分析
- 影响因素回归分析

### 4.2 管控策略优化模型
- 目标函数设计
- 约束条件分析
- 求解算法选择

### 4.3 模型验证
- 历史数据验证
- 敏感性分析

## 5. 结果分析
### 5.1 数据分析结果
- 流量变化规律
- 管控效果评估

### 5.2 模型求解结果
- 最优管控策略
- 效果预测

## 6. 模型评价与推广
### 6.1 模型优缺点分析
### 6.2 适用范围讨论
### 6.3 改进建议

## 7. 结论与建议
- 主要研究结论
- 实际应用建议
- 进一步研究方向

## 参考文献

## 附录
- 数据处理代码
- 详细计算过程
- 补充图表
"""
    
    with open("论文大纲.md", "w", encoding="utf-8") as f:
        f.write(outline)
    
    print("论文大纲已生成: 论文大纲.md")

if __name__ == "__main__":
    # 分析数据
    load_and_analyze_data()
    
    # 生成论文大纲
    generate_paper_outline()
    
    print("\n=== 分析完成 ===")
    print("请查看生成的分析文件:")
    print("- csv_analysis.txt (CSV数据分析)")
    print("- excel_analysis_*.txt (Excel数据分析)")
    print("- 论文大纲.md (论文结构)")
